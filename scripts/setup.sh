#!/bin/bash

# EduAI Global Academy Hub - Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up EduAI Global Academy Hub..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "⚠️  Docker is not installed. Some features may not work."
else
    echo "✅ Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1) detected"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please update the .env file with your configuration"
fi

# Create logs directory
mkdir -p logs

# Build shared package
echo "🔨 Building shared package..."
npm run build:shared

# Check if PostgreSQL and Redis are running (for local development)
echo "🔍 Checking database connections..."

if command -v pg_isready &> /dev/null; then
    if pg_isready -h localhost -p 5432 &> /dev/null; then
        echo "✅ PostgreSQL is running"
    else
        echo "⚠️  PostgreSQL is not running. You can start it with Docker:"
        echo "   docker-compose up postgres -d"
    fi
else
    echo "⚠️  PostgreSQL client not found. Using Docker is recommended."
fi

if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis is running"
    else
        echo "⚠️  Redis is not running. You can start it with Docker:"
        echo "   docker-compose up redis -d"
    fi
else
    echo "⚠️  Redis client not found. Using Docker is recommended."
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update the .env file with your configuration"
echo "2. Start the database services: docker-compose up postgres redis -d"
echo "3. Start the development servers: npm run dev"
echo ""
echo "Available commands:"
echo "  npm run dev          - Start both frontend and backend"
echo "  npm run dev:frontend - Start only frontend"
echo "  npm run dev:backend  - Start only backend"
echo "  npm run build        - Build all packages"
echo "  npm run test         - Run all tests"
echo "  npm run lint         - Lint all packages"
echo ""
echo "Docker commands:"
echo "  docker-compose up -d              - Start all services"
echo "  docker-compose up postgres redis -d  - Start only database services"
echo "  docker-compose down               - Stop all services"
echo ""
