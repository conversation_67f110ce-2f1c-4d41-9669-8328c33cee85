{"name": "@eduai/backend", "version": "1.0.0", "description": "Backend service for EduAI Global Academy Hub", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "clean": "rimraf dist node_modules/.cache", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "tsx src/scripts/seed.ts"}, "dependencies": {"@eduai/shared": "workspace:*", "@prisma/client": "^5.7.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "redis": "^4.6.10", "winston": "^3.11.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "prettier": "^3.1.0", "prisma": "^5.7.0", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.5.3"}}