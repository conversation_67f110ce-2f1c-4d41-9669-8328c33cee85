{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@eduai/shared": ["../../packages/shared/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}