import { Router } from 'express';
import { z } from 'zod';
import { logger } from '../utils/logger.js';

const router = Router();

// Mock community data
const mockPosts = [
  {
    id: 1,
    title: "Tips for getting started with Machine Learning",
    content: "I've been learning ML for 6 months now and wanted to share some tips that helped me...",
    author: {
      id: '2',
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face'
    },
    createdAt: '2024-01-20T10:00:00Z',
    likes: 24,
    replies: 8,
    tags: ['machine-learning', 'beginner', 'tips']
  },
  {
    id: 2,
    title: "Study Group for Deep Learning Course",
    content: "Looking for study partners for the Deep Learning course. Let's form a group!",
    author: {
      id: '3',
      name: '<PERSON>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face'
    },
    createdAt: '2024-01-19T15:30:00Z',
    likes: 12,
    replies: 15,
    tags: ['deep-learning', 'study-group', 'collaboration']
  },
  {
    id: 3,
    title: "My first AI project - feedback welcome!",
    content: "Just finished my first AI project - a simple chatbot. Would love to get feedback from the community!",
    author: {
      id: '4',
      name: 'Carol Davis',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face'
    },
    createdAt: '2024-01-18T09:15:00Z',
    likes: 31,
    replies: 22,
    tags: ['project', 'chatbot', 'feedback']
  }
];

const mockStudyGroups = [
  {
    id: 1,
    name: 'ML Beginners Circle',
    description: 'A supportive group for those starting their ML journey',
    members: 156,
    isPublic: true,
    tags: ['machine-learning', 'beginner'],
    lastActivity: '2024-01-20T14:30:00Z'
  },
  {
    id: 2,
    name: 'Deep Learning Masters',
    description: 'Advanced discussions on deep learning techniques',
    members: 89,
    isPublic: true,
    tags: ['deep-learning', 'advanced'],
    lastActivity: '2024-01-20T11:45:00Z'
  },
  {
    id: 3,
    name: 'NLP Enthusiasts',
    description: 'Everything about Natural Language Processing',
    members: 203,
    isPublic: true,
    tags: ['nlp', 'text-processing'],
    lastActivity: '2024-01-19T16:20:00Z'
  }
];

// Validation schemas
const createPostSchema = z.object({
  title: z.string().min(5).max(200),
  content: z.string().min(10).max(5000),
  tags: z.array(z.string()).max(5).optional()
});

// GET /api/v1/community/posts
router.get('/posts', async (req, res, next) => {
  try {
    const { tag, limit = 10, offset = 0 } = req.query;
    
    let filteredPosts = [...mockPosts];
    
    // Filter by tag
    if (tag && typeof tag === 'string') {
      filteredPosts = filteredPosts.filter(post =>
        post.tags.includes(tag.toLowerCase())
      );
    }
    
    // Sort by creation date (newest first)
    filteredPosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    // Pagination
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex);
    
    logger.info(`Fetched ${paginatedPosts.length} community posts`);
    
    res.json({
      success: true,
      data: {
        posts: paginatedPosts,
        total: filteredPosts.length,
        limit: Number(limit),
        offset: Number(offset)
      }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/v1/community/posts
router.post('/posts', async (req, res, next) => {
  try {
    const { title, content, tags } = createPostSchema.parse(req.body);
    
    // TODO: Create post in database
    const newPost = {
      id: mockPosts.length + 1,
      title,
      content,
      author: {
        id: '1',
        name: 'John Doe',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'
      },
      createdAt: new Date().toISOString(),
      likes: 0,
      replies: 0,
      tags: tags || []
    };
    
    logger.info(`Created new community post: ${title}`);
    
    res.status(201).json({
      success: true,
      data: { post: newPost }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/v1/community/study-groups
router.get('/study-groups', async (req, res, next) => {
  try {
    const { tag, limit = 10, offset = 0 } = req.query;
    
    let filteredGroups = [...mockStudyGroups];
    
    // Filter by tag
    if (tag && typeof tag === 'string') {
      filteredGroups = filteredGroups.filter(group =>
        group.tags.includes(tag.toLowerCase())
      );
    }
    
    // Sort by member count (most popular first)
    filteredGroups.sort((a, b) => b.members - a.members);
    
    // Pagination
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedGroups = filteredGroups.slice(startIndex, endIndex);
    
    logger.info(`Fetched ${paginatedGroups.length} study groups`);
    
    res.json({
      success: true,
      data: {
        studyGroups: paginatedGroups,
        total: filteredGroups.length,
        limit: Number(limit),
        offset: Number(offset)
      }
    });
  } catch (error) {
    next(error);
  }
});

export { router as communityRoutes };
