import { Router } from 'express';
import { z } from 'zod';
import { logger } from '../utils/logger.js';

const router = Router();

// Mock course data that matches your frontend
const mockCourses = [
  {
    id: 1,
    title: "Introduction to Machine Learning",
    description: "Learn the fundamentals of ML with hands-on projects",
    level: "Beginner",
    duration: "6 weeks",
    students: 12500,
    rating: 4.8,
    isPremium: false,
    image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=400&h=250&fit=crop",
    instructor: "Dr. <PERSON>",
    price: 0,
    topics: ["Python", "Scikit-learn", "Data Analysis", "Supervised Learning"]
  },
  {
    id: 2,
    title: "Deep Learning with Neural Networks",
    description: "Master neural networks and deep learning techniques",
    level: "Advanced",
    duration: "8 weeks",
    students: 8200,
    rating: 4.9,
    isPremium: true,
    image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=250&fit=crop",
    instructor: "<PERSON><PERSON> <PERSON>",
    price: 199,
    topics: ["TensorFlow", "PyTorch", "CNNs", "RNNs", "GANs"]
  },
  {
    id: 3,
    title: "Natural Language Processing",
    description: "Build chatbots and text analysis applications",
    level: "Intermediate",
    duration: "7 weeks",
    students: 9800,
    rating: 4.7,
    isPremium: false,
    image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=250&fit=crop",
    instructor: "Dr. Emily Rodriguez",
    price: 99,
    topics: ["NLTK", "spaCy", "Transformers", "BERT", "GPT"]
  }
];

// GET /api/v1/courses
router.get('/', async (req, res, next) => {
  try {
    const { level, search, limit = 10, offset = 0 } = req.query;
    
    let filteredCourses = [...mockCourses];
    
    // Filter by level
    if (level && typeof level === 'string') {
      filteredCourses = filteredCourses.filter(course => 
        course.level.toLowerCase() === level.toLowerCase()
      );
    }
    
    // Search functionality
    if (search && typeof search === 'string') {
      const searchTerm = search.toLowerCase();
      filteredCourses = filteredCourses.filter(course =>
        course.title.toLowerCase().includes(searchTerm) ||
        course.description.toLowerCase().includes(searchTerm) ||
        course.topics.some(topic => topic.toLowerCase().includes(searchTerm))
      );
    }
    
    // Pagination
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedCourses = filteredCourses.slice(startIndex, endIndex);
    
    logger.info(`Fetched ${paginatedCourses.length} courses`);
    
    res.json({
      success: true,
      data: {
        courses: paginatedCourses,
        total: filteredCourses.length,
        limit: Number(limit),
        offset: Number(offset)
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/v1/courses/:id
router.get('/:id', async (req, res, next) => {
  try {
    const courseId = parseInt(req.params.id);
    const course = mockCourses.find(c => c.id === courseId);
    
    if (!course) {
      return res.status(404).json({
        success: false,
        error: { message: 'Course not found' }
      });
    }
    
    logger.info(`Fetched course: ${course.title}`);
    
    res.json({
      success: true,
      data: { course }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/v1/courses/featured
router.get('/featured', async (req, res, next) => {
  try {
    // Return top 3 courses by rating
    const featuredCourses = mockCourses
      .sort((a, b) => b.rating - a.rating)
      .slice(0, 3);
    
    logger.info(`Fetched ${featuredCourses.length} featured courses`);
    
    res.json({
      success: true,
      data: { courses: featuredCourses }
    });
  } catch (error) {
    next(error);
  }
});

export { router as courseRoutes };
