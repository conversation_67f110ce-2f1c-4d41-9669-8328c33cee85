
import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { MessageSquare, Users, Calendar, Globe, Search, Plus, Heart, Reply, Share } from "lucide-react";

const Community = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const discussions = [
    {
      id: 1,
      title: "Best practices for training neural networks?",
      content: "I'm working on my first deep learning project and struggling with training stability. Any tips for avoiding overfitting?",
      author: "<PERSON>",
      avatar: "SC",
      timestamp: "2 hours ago",
      category: "Deep Learning",
      replies: 23,
      likes: 45,
      tags: ["neural-networks", "training", "overfitting"]
    },
    {
      id: 2,
      title: "Implementing attention mechanisms in transformers",
      content: "Can someone explain the intuition behind self-attention? I understand the math but struggle with the conceptual understanding.",
      author: "<PERSON>",
      avatar: "AK",
      timestamp: "4 hours ago",
      category: "NLP",
      replies: 18,
      likes: 32,
      tags: ["transformers", "attention", "nlp"]
    },
    {
      id: 3,
      title: "Career transition from software dev to ML engineer",
      content: "I've been a full-stack developer for 5 years and want to transition to ML. What's the best learning path?",
      author: "Maria Rodriguez",
      avatar: "MR",
      timestamp: "6 hours ago",
      category: "Career",
      replies: 41,
      likes: 78,
      tags: ["career", "transition", "ml-engineering"]
    }
  ];

  const studyGroups = [
    {
      id: 1,
      name: "Deep Learning Study Group",
      description: "Weekly sessions on neural networks and deep learning concepts",
      members: 156,
      timezone: "UTC-5 (EST)",
      nextMeeting: "Tomorrow, 7:00 PM",
      language: "English",
      level: "Intermediate"
    },
    {
      id: 2,
      name: "NLP Reading Club",
      description: "Monthly paper discussions on latest NLP research",
      members: 89,
      timezone: "UTC+0 (GMT)",
      nextMeeting: "Friday, 3:00 PM",
      language: "English",
      level: "Advanced"
    },
    {
      id: 3,
      name: "AI for Beginners",
      description: "Supportive community for those starting their AI journey",
      members: 234,
      timezone: "UTC+8 (CST)",
      nextMeeting: "Sunday, 10:00 AM",
      language: "Mandarin",
      level: "Beginner"
    }
  ];

  const mentors = [
    {
      id: 1,
      name: "Dr. Emily Watson",
      title: "Senior AI Researcher at Google",
      expertise: ["Machine Learning", "Computer Vision", "Research"],
      rating: 4.9,
      sessions: 127,
      image: "/placeholder.svg"
    },
    {
      id: 2,
      name: "Marcus Johnson",
      title: "ML Engineering Lead at Tesla",
      expertise: ["Deep Learning", "Autonomous Systems", "Production ML"],
      rating: 4.8,
      sessions: 89,
      image: "/placeholder.svg"
    },
    {
      id: 3,
      name: "Priya Patel",
      title: "Data Science Director at Netflix",
      expertise: ["NLP", "Recommendation Systems", "Data Strategy"],
      rating: 4.9,
      sessions: 156,
      image: "/placeholder.svg"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Global AI Community
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Connect with AI enthusiasts worldwide, join study groups, and learn from industry experts
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="discussions" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 lg:w-96 mx-auto">
            <TabsTrigger value="discussions">Discussions</TabsTrigger>
            <TabsTrigger value="study-groups">Study Groups</TabsTrigger>
            <TabsTrigger value="mentorship">Mentorship</TabsTrigger>
          </TabsList>

          {/* Discussions Tab */}
          <TabsContent value="discussions" className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <Input
                      placeholder="Search discussions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  New Discussion
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              {discussions.map((discussion) => (
                <Card key={discussion.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <Avatar>
                          <AvatarFallback>{discussion.avatar}</AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-lg mb-1">{discussion.title}</CardTitle>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <span>{discussion.author}</span>
                            <span>•</span>
                            <span>{discussion.timestamp}</span>
                          </div>
                        </div>
                      </div>
                      <Badge>{discussion.category}</Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <p className="text-gray-700 mb-4">{discussion.content}</p>
                    
                    <div className="flex flex-wrap gap-2 mb-4">
                      {discussion.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          #{tag}
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <Button variant="ghost" size="sm">
                          <Heart className="w-4 h-4 mr-1" />
                          {discussion.likes}
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Reply className="w-4 h-4 mr-1" />
                          {discussion.replies}
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Share className="w-4 h-4 mr-1" />
                          Share
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Study Groups Tab */}
          <TabsContent value="study-groups" className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                  <h2 className="text-xl font-semibold mb-2">Study Groups</h2>
                  <p className="text-gray-600">Join study groups based on your timezone and interests</p>
                </div>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Group
                </Button>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              {studyGroups.map((group) => (
                <Card key={group.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      {group.name}
                      <Badge>{group.level}</Badge>
                    </CardTitle>
                    <CardDescription>{group.description}</CardDescription>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2 text-sm">
                        <Users className="w-4 h-4 text-gray-500" />
                        <span>{group.members} members</span>
                      </div>
                      
                      <div className="flex items-center space-x-2 text-sm">
                        <Globe className="w-4 h-4 text-gray-500" />
                        <span>{group.timezone}</span>
                      </div>
                      
                      <div className="flex items-center space-x-2 text-sm">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <span>Next: {group.nextMeeting}</span>
                      </div>
                      
                      <div className="flex items-center space-x-2 text-sm">
                        <MessageSquare className="w-4 h-4 text-gray-500" />
                        <span>Language: {group.language}</span>
                      </div>
                    </div>
                    
                    <Button className="w-full mt-4">
                      Join Group
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Mentorship Tab */}
          <TabsContent value="mentorship" className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-2">Expert Mentorship</h2>
                <p className="text-gray-600">Get personalized guidance from industry professionals</p>
              </div>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {mentors.map((mentor) => (
                <Card key={mentor.id} className="text-center">
                  <CardHeader>
                    <Avatar className="w-20 h-20 mx-auto mb-4">
                      <AvatarImage src={mentor.image} />
                      <AvatarFallback>{mentor.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                    </Avatar>
                    
                    <CardTitle className="text-lg">{mentor.name}</CardTitle>
                    <CardDescription>{mentor.title}</CardDescription>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex flex-wrap gap-1 justify-center">
                        {mentor.expertise.map((skill) => (
                          <Badge key={skill} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                      
                      <div className="text-sm text-gray-600">
                        ⭐ {mentor.rating} • {mentor.sessions} sessions
                      </div>
                    </div>
                    
                    <Button className="w-full mt-4">
                      Book Session
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Community;
