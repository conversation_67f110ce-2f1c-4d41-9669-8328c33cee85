
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Globe, Users, Award, BookOpen, Target, Heart, Lightbulb, Zap } from "lucide-react";

const About = () => {
  const stats = [
    { label: "Active Learners", value: "2.3M+", icon: Users },
    { label: "Countries Served", value: "150+", icon: Globe },
    { label: "Courses Available", value: "500+", icon: BookOpen },
    { label: "Certificates Issued", value: "180K+", icon: Award }
  ];

  const values = [
    {
      icon: Globe,
      title: "Global Accessibility",
      description: "Making AI education accessible to learners worldwide, regardless of location or background"
    },
    {
      icon: Heart,
      title: "Inclusive Learning",
      description: "Creating a welcoming environment where everyone can learn and grow together"
    },
    {
      icon: Lightbulb,
      title: "Innovation-Driven",
      description: "Constantly evolving our platform with the latest AI technologies and pedagogical methods"
    },
    {
      icon: Zap,
      title: "Impact-Focused",
      description: "Empowering learners to create positive change in their communities and careers"
    }
  ];

  const team = [
    {
      name: "<PERSON>",
      role: "CEO & Co-Founder",
      bio: "Former Google AI researcher with 15+ years in ML education",
      image: "/placeholder.svg"
    },
    {
      name: "Dr. Michael Chen",
      role: "CTO & Co-Founder",
      bio: "PhD in Computer Science, expert in scalable learning platforms",
      image: "/placeholder.svg"
    },
    {
      name: "Priya Patel",
      role: "Head of Curriculum",
      bio: "Former Stanford professor specializing in AI pedagogy",
      image: "/placeholder.svg"
    },
    {
      name: "Alex Rodriguez",
      role: "Head of Community",
      bio: "Community building expert with passion for global education",
      image: "/placeholder.svg"
    }
  ];

  const milestones = [
    {
      year: "2020",
      title: "Foundation",
      description: "EduAI Global founded with mission to democratize AI education"
    },
    {
      year: "2021",
      title: "Global Launch",
      description: "Platform launched in 25 languages across 50 countries"
    },
    {
      year: "2022",
      title: "1M Learners",
      description: "Reached first million active learners milestone"
    },
    {
      year: "2023",
      title: "Industry Recognition",
      description: "Awarded 'Best EdTech Platform' by Global Education Awards"
    },
    {
      year: "2024",
      title: "AI Innovation",
      description: "Launched personalized AI learning paths and mentorship matching"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Democratizing AI Education
              <br />
              <span className="text-blue-200">Worldwide</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Our mission is to make world-class AI education accessible to everyone, 
              everywhere, empowering the next generation of AI innovators.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
                Join Our Mission
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8 py-3 bg-transparent border-white text-white hover:bg-white hover:text-blue-600">
                View Impact Report
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <stat.icon className="w-8 h-8 mx-auto mb-3 text-blue-600" />
                  <div className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <Target className="w-8 h-8 text-blue-600" />
                <h2 className="text-3xl font-bold text-gray-900">Our Mission</h2>
              </div>
              <p className="text-lg text-gray-700 mb-6">
                To democratize AI education globally through an accessible online platform 
                that connects learners with world-class content, expert mentorship, and a 
                vibrant community of AI enthusiasts.
              </p>
              <p className="text-gray-600">
                We believe that everyone deserves access to quality AI education, regardless 
                of their geographical location, economic background, or prior experience. 
                Our platform breaks down barriers and creates opportunities for millions 
                of learners worldwide.
              </p>
            </div>
            
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <Lightbulb className="w-8 h-8 text-indigo-600" />
                <h2 className="text-3xl font-bold text-gray-900">Our Vision</h2>
              </div>
              <p className="text-lg text-gray-700 mb-6">
                To serve 50 million learners by 2030 across 150+ countries, creating 
                the world's most comprehensive and inclusive AI education ecosystem.
              </p>
              <p className="text-gray-600">
                We envision a future where AI literacy is universal, where innovation 
                knows no borders, and where the next breakthrough in artificial intelligence 
                could come from anywhere in the world.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-lg text-gray-600">
              The principles that guide everything we do
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <value.icon className="w-12 h-12 mx-auto mb-4 text-blue-600" />
                  <h3 className="text-xl font-semibold mb-3">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Journey
            </h2>
            <p className="text-lg text-gray-600">
              Key milestones in our mission to democratize AI education
            </p>
          </div>
          
          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                    {milestone.year.slice(-2)}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="secondary">{milestone.year}</Badge>
                    <h3 className="text-xl font-semibold text-gray-900">{milestone.title}</h3>
                  </div>
                  <p className="text-gray-600">{milestone.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Leadership Team
            </h2>
            <p className="text-lg text-gray-600">
              Meet the passionate team driving our mission forward
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center">
                <CardHeader>
                  <Avatar className="w-24 h-24 mx-auto mb-4">
                    <AvatarImage src={member.image} />
                    <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <CardTitle className="text-lg">{member.name}</CardTitle>
                  <CardDescription className="font-medium text-blue-600">{member.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">{member.bio}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Join Us in Shaping the Future
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Whether you're a learner, educator, or industry professional, 
            there's a place for you in our global AI community.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
              Start Learning Today
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-3 bg-transparent border-white text-white hover:bg-white hover:text-blue-600">
              Partner With Us
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
