{"name": "@eduai/shared", "version": "1.0.0", "description": "Shared types, utilities, and constants for EduAI Global Academy Hub", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"zod": "^3.23.8"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "typescript": "^5.5.3"}}