import { z } from 'zod';

// User-related schemas and types
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  bio: z.string().optional(),
  avatar: z.string().url().optional(),
  joinedAt: z.string().datetime(),
  coursesCompleted: z.number().int().min(0).default(0),
  certificatesEarned: z.number().int().min(0).default(0),
  studyStreak: z.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
  role: z.enum(['student', 'instructor', 'admin']).default('student'),
});

export const UserProfileSchema = UserSchema.pick({
  id: true,
  email: true,
  firstName: true,
  lastName: true,
  bio: true,
  avatar: true,
  joinedAt: true,
  coursesCompleted: true,
  certificatesEarned: true,
  studyStreak: true,
});

export const UpdateUserProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  bio: z.string().max(500).optional(),
  avatar: z.string().url().optional(),
});

export const LoginCredentialsSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export const RegisterCredentialsSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
});

export const AuthResponseSchema = z.object({
  user: UserProfileSchema,
  token: z.string(),
});

// TypeScript types derived from schemas
export type User = z.infer<typeof UserSchema>;
export type UserProfile = z.infer<typeof UserProfileSchema>;
export type UpdateUserProfile = z.infer<typeof UpdateUserProfileSchema>;
export type LoginCredentials = z.infer<typeof LoginCredentialsSchema>;
export type RegisterCredentials = z.infer<typeof RegisterCredentialsSchema>;
export type AuthResponse = z.infer<typeof AuthResponseSchema>;

// User role enum
export enum UserRole {
  STUDENT = 'student',
  INSTRUCTOR = 'instructor',
  ADMIN = 'admin',
}
