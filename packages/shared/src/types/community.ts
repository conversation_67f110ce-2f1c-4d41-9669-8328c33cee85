import { z } from 'zod';

// Community-related schemas and types
export const AuthorSchema = z.object({
  id: z.string(),
  name: z.string().min(1),
  avatar: z.string().url().optional(),
});

export const CommunityPostSchema = z.object({
  id: z.number().int().positive(),
  title: z.string().min(5).max(200),
  content: z.string().min(10).max(5000),
  author: AuthorSchema,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime().optional(),
  likes: z.number().int().min(0).default(0),
  replies: z.number().int().min(0).default(0),
  tags: z.array(z.string()).max(5).default([]),
  isLocked: z.boolean().default(false),
  isPinned: z.boolean().default(false),
});

export const CreatePostSchema = z.object({
  title: z.string().min(5).max(200),
  content: z.string().min(10).max(5000),
  tags: z.array(z.string()).max(5).optional(),
});

export const UpdatePostSchema = z.object({
  title: z.string().min(5).max(200).optional(),
  content: z.string().min(10).max(5000).optional(),
  tags: z.array(z.string()).max(5).optional(),
});

export const StudyGroupSchema = z.object({
  id: z.number().int().positive(),
  name: z.string().min(1).max(100),
  description: z.string().min(10).max(500),
  members: z.number().int().min(0).default(0),
  maxMembers: z.number().int().positive().optional(),
  isPublic: z.boolean().default(true),
  tags: z.array(z.string()).max(5).default([]),
  createdAt: z.string().datetime(),
  lastActivity: z.string().datetime(),
  createdBy: AuthorSchema,
});

export const CreateStudyGroupSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().min(10).max(500),
  maxMembers: z.number().int().positive().optional(),
  isPublic: z.boolean().default(true),
  tags: z.array(z.string()).max(5).optional(),
});

export const PostsQuerySchema = z.object({
  tag: z.string().optional(),
  author: z.string().optional(),
  search: z.string().optional(),
  limit: z.number().int().positive().max(50).default(10),
  offset: z.number().int().min(0).default(0),
  sortBy: z.enum(['newest', 'oldest', 'popular']).default('newest'),
});

export const StudyGroupsQuerySchema = z.object({
  tag: z.string().optional(),
  search: z.string().optional(),
  limit: z.number().int().positive().max(50).default(10),
  offset: z.number().int().min(0).default(0),
  sortBy: z.enum(['popular', 'newest', 'active']).default('popular'),
});

export const AchievementSchema = z.object({
  id: z.number().int().positive(),
  title: z.string().min(1),
  description: z.string().min(1),
  icon: z.string().min(1),
  earnedAt: z.string().datetime(),
  category: z.enum(['learning', 'community', 'streak', 'milestone']).optional(),
});

// TypeScript types derived from schemas
export type Author = z.infer<typeof AuthorSchema>;
export type CommunityPost = z.infer<typeof CommunityPostSchema>;
export type CreatePost = z.infer<typeof CreatePostSchema>;
export type UpdatePost = z.infer<typeof UpdatePostSchema>;
export type StudyGroup = z.infer<typeof StudyGroupSchema>;
export type CreateStudyGroup = z.infer<typeof CreateStudyGroupSchema>;
export type PostsQuery = z.infer<typeof PostsQuerySchema>;
export type StudyGroupsQuery = z.infer<typeof StudyGroupsQuerySchema>;
export type Achievement = z.infer<typeof AchievementSchema>;

// Community enums
export enum PostSortBy {
  NEWEST = 'newest',
  OLDEST = 'oldest',
  POPULAR = 'popular',
}

export enum StudyGroupSortBy {
  POPULAR = 'popular',
  NEWEST = 'newest',
  ACTIVE = 'active',
}

export enum AchievementCategory {
  LEARNING = 'learning',
  COMMUNITY = 'community',
  STREAK = 'streak',
  MILESTONE = 'milestone',
}
