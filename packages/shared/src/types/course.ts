import { z } from 'zod';

// Course-related schemas and types
export const CourseSchema = z.object({
  id: z.number().int().positive(),
  title: z.string().min(1),
  description: z.string().min(1),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']),
  duration: z.string(),
  students: z.number().int().min(0),
  rating: z.number().min(0).max(5),
  isPremium: z.boolean(),
  image: z.string().url(),
  instructor: z.string().min(1),
  price: z.number().min(0),
  topics: z.array(z.string()),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
});

export const CourseProgressSchema = z.object({
  id: z.number().int().positive(),
  title: z.string().min(1),
  progress: z.number().min(0).max(100),
  lastAccessed: z.string().datetime(),
  completedLessons: z.number().int().min(0),
  totalLessons: z.number().int().positive(),
});

export const CoursesQuerySchema = z.object({
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  search: z.string().optional(),
  limit: z.number().int().positive().max(100).default(10),
  offset: z.number().int().min(0).default(0),
});

export const CreateCourseSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().min(10).max(2000),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']),
  duration: z.string().min(1),
  isPremium: z.boolean().default(false),
  image: z.string().url(),
  instructor: z.string().min(1),
  price: z.number().min(0),
  topics: z.array(z.string()).min(1).max(10),
});

export const UpdateCourseSchema = CreateCourseSchema.partial();

// TypeScript types derived from schemas
export type Course = z.infer<typeof CourseSchema>;
export type CourseProgress = z.infer<typeof CourseProgressSchema>;
export type CoursesQuery = z.infer<typeof CoursesQuerySchema>;
export type CreateCourse = z.infer<typeof CreateCourseSchema>;
export type UpdateCourse = z.infer<typeof UpdateCourseSchema>;

// Course level enum
export enum CourseLevel {
  BEGINNER = 'Beginner',
  INTERMEDIATE = 'Intermediate',
  ADVANCED = 'Advanced',
}

// Course enrollment status
export enum EnrollmentStatus {
  NOT_ENROLLED = 'not_enrolled',
  ENROLLED = 'enrolled',
  COMPLETED = 'completed',
  DROPPED = 'dropped',
}
