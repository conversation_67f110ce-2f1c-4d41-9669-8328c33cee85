import { z } from 'zod';

/**
 * Utility function to validate data against a Zod schema
 * @param schema - The Zod schema to validate against
 * @param data - The data to validate
 * @returns Validation result with success flag and data or errors
 */
export function validateData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: z.ZodError } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error };
    }
    throw error;
  }
}

/**
 * Utility function to safely parse data with a Zod schema
 * @param schema - The Zod schema to validate against
 * @param data - The data to validate
 * @returns Parsed data or null if validation fails
 */
export function safeParseData<T>(schema: z.ZodSchema<T>, data: unknown): T | null {
  const result = schema.safeParse(data);
  return result.success ? result.data : null;
}

/**
 * Format Zod validation errors into a user-friendly format
 * @param error - The Zod error object
 * @returns Formatted error messages
 */
export function formatValidationErrors(error: z.ZodError): Record<string, string[]> {
  const formattedErrors: Record<string, string[]> = {};
  
  error.errors.forEach((err) => {
    const path = err.path.join('.');
    if (!formattedErrors[path]) {
      formattedErrors[path] = [];
    }
    formattedErrors[path].push(err.message);
  });
  
  return formattedErrors;
}

/**
 * Get a simple error message from Zod validation errors
 * @param error - The Zod error object
 * @returns Simple error message string
 */
export function getSimpleErrorMessage(error: z.ZodError): string {
  const firstError = error.errors[0];
  if (!firstError) return 'Validation failed';
  
  const path = firstError.path.length > 0 ? `${firstError.path.join('.')}: ` : '';
  return `${path}${firstError.message}`;
}

/**
 * Common validation patterns
 */
export const ValidationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  url: /^https?:\/\/.+/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
} as const;

/**
 * Custom Zod validators
 */
export const customValidators = {
  strongPassword: z.string().refine(
    (password) => ValidationPatterns.password.test(password),
    {
      message: 'Password must contain at least 8 characters, one uppercase, one lowercase, and one number',
    }
  ),
  
  phoneNumber: z.string().refine(
    (phone) => ValidationPatterns.phone.test(phone),
    {
      message: 'Invalid phone number format',
    }
  ),
  
  slug: z.string().refine(
    (slug) => ValidationPatterns.slug.test(slug),
    {
      message: 'Slug must contain only lowercase letters, numbers, and hyphens',
    }
  ),
} as const;
