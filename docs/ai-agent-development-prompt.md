# EduAI Global Platform Development Prompt

## Project Overview
You are tasked with building **EduAI Global**, a comprehensive AI education platform designed to democratize AI learning worldwide. This is a production-ready monorepo application using FastAPI (Python) backend and React (TypeScript) frontend.

## Mission & Vision
- **Mission**: Democratize AI education globally through an accessible online platform
- **Vision**: Serve 50 million learners by 2030 across 150+ countries
- **Target Users**: Working professionals (35-50), university students (18-25), entrepreneurs, and educators

## Technical Architecture Requirements

### Backend (FastAPI + Python)
```
Technology Stack:
- FastAPI (async API framework)
- Poetry (dependency management)
- Pydantic (data validation)
- SQLAlchemy + Alembic (ORM + migrations)
- PostgreSQL (primary database with JSONB support)
- Redis (caching, sessions, real-time features)
- Celery (background task processing)
- OpenAI API (AI recommendations)
- LangChain (AI orchestration)
- Pinecone (vector database)
- Stripe (payments)
- SendGrid (emails)
```

### Frontend (React + TypeScript)
```
Technology Stack:
- React 18 + Vite (modern React with fast builds)
- TypeScript (type safety)
- TanStack Query (server state management)
- React Router (client-side routing)
- <PERSON>ustand (lightweight state management)
- Material-UI (MUI) (component library)
- PWA capabilities (offline-first mobile)
```

### Infrastructure
```
- Docker + Docker Compose (containerization)
- GitHub Actions (CI/CD)
- Cloudflare (CDN, DDoS protection)
- AWS/GCP (cloud hosting)
- Nginx (reverse proxy)
- Sentry (error monitoring)
```

## Core Features to Implement

### 1. User Management & Authentication
- User registration/login with JWT tokens
- Multi-language support (25+ languages)
- User profiles with learning preferences
- Role-based access control (free, premium, corporate, admin)
- Password reset functionality
- Social media login integration

### 2. Course Management System
- Course catalog with filtering/search
- Multi-language course content (stored in JSONB)
- Course enrollment system
- Module-based course structure
- Video content delivery
- Interactive coding environments
- Quiz and assessment system
- Progress tracking and analytics

### 3. AI-Powered Features
- Personalized course recommendations using OpenAI API
- Adaptive learning paths based on user progress
- AI-powered content analysis and tagging
- Semantic search using vector embeddings
- Learning outcome prediction

### 4. Community Features
- Study groups with timezone/language matching
- Mentorship booking system
- Discussion forums
- Real-time messaging (WebSockets)
- Project portfolio showcase
- Peer collaboration tools

### 5. Certification & Credentials
- Industry-recognized certificates
- Skill assessments and badges
- Portfolio project validation
- LinkedIn integration for credential sharing

### 6. Payment & Subscription System
- Stripe integration for payments
- Freemium model (free tier + premium subscriptions)
- Regional pricing based on purchasing power
- Corporate bulk licensing
- Subscription management

### 7. Mobile & PWA Features
- Progressive Web App (PWA) with offline capability
- Mobile-responsive design
- Push notifications
- Offline content caching
- Cross-device progress synchronization

## Database Schema Requirements

### Core Tables (PostgreSQL with JSONB)
```sql
-- Users table with multi-language support
users (
  id UUID PRIMARY KEY,
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR NOT NULL,
  preferred_language VARCHAR(5) DEFAULT 'en',
  timezone VARCHAR(50),
  subscription_tier VARCHAR(20) DEFAULT 'free',
  profile_data JSONB, -- Flexible user preferences
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- Courses with multi-language content
courses (
  id UUID PRIMARY KEY,
  title JSONB NOT NULL, -- Multi-language titles
  description JSONB,
  difficulty_level VARCHAR(20),
  estimated_hours INTEGER,
  prerequisites JSONB,
  tags TEXT[],
  is_premium BOOLEAN DEFAULT false,
  content_metadata JSONB,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- Learning progress tracking
user_progress (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  course_id UUID REFERENCES courses(id),
  module_id UUID REFERENCES modules(id),
  completion_percentage INTEGER DEFAULT 0,
  last_accessed TIMESTAMP,
  time_spent INTEGER, -- seconds
  quiz_scores JSONB,
  ai_insights JSONB, -- Personalization data
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- Additional tables for modules, study_groups, mentorship_sessions, subscriptions, etc.
```

## Monorepo Structure
```
eduai-global/
├── backend/                    # FastAPI Python backend
│   ├── app/
│   │   ├── core/              # Configuration, security, database
│   │   ├── models/            # SQLAlchemy models
│   │   ├── schemas/           # Pydantic schemas
│   │   ├── services/          # Business logic
│   │   ├── routes/            # API endpoints
│   │   └── utils/             # Helper functions
│   ├── tests/                 # Backend tests
│   ├── migrations/            # Alembic database migrations
│   ├── pyproject.toml         # Poetry configuration
│   └── Dockerfile
├── frontend/                  # React TypeScript frontend
│   ├── src/
│   │   ├── components/        # Reusable UI components
│   │   ├── pages/             # Route components
│   │   ├── hooks/             # Custom React hooks
│   │   ├── services/          # API client services
│   │   ├── store/             # Zustand state management
│   │   ├── utils/             # Helper functions
│   │   └── types/             # TypeScript definitions
│   ├── public/                # Static assets
│   ├── tests/                 # Frontend tests
│   ├── package.json
│   └── Dockerfile
├── shared/                    # Shared types and utilities
├── docs/                      # Project documentation
├── infrastructure/            # Docker, deployment configs
├── scripts/                   # Build and deployment scripts
├── .github/workflows/         # CI/CD pipelines
├── docker-compose.yml         # Local development environment
└── README.md
```

## Key Implementation Requirements

### 1. Global Scalability
- Multi-region deployment support
- CDN integration for content delivery
- Efficient caching strategies
- Database optimization for global users

### 2. Accessibility & Inclusivity
- WCAG 2.1 AA compliance
- Screen reader compatibility
- Keyboard navigation support
- Multi-language UI (i18n)
- Offline-first design for emerging markets

### 3. Security
- JWT-based authentication with refresh tokens
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Rate limiting
- HTTPS enforcement

### 4. Performance
- Response times < 2s for 95th percentile
- Database query optimization
- Efficient caching (Redis)
- Image optimization and lazy loading
- Code splitting and bundle optimization

### 5. Testing Strategy
- Unit tests for all business logic
- Integration tests for API endpoints
- End-to-end tests for critical user journeys
- Performance testing for concurrent users
- Security testing for vulnerabilities

## Business Logic Requirements

### User Stories to Implement (Priority Order)
1. **P0 (Critical)**: User registration, login, course browsing, enrollment, basic progress tracking
2. **P1 (High)**: AI recommendations, community features, payment processing, mobile PWA
3. **P2 (Medium)**: Advanced analytics, mentorship system, certification generation

### AI Integration Specifics
- Use OpenAI API for content personalization
- Implement vector search with Pinecone for semantic course matching
- Create learning path optimization algorithms
- Build recommendation engine based on user behavior

### Internationalization Requirements
- Support for 25+ languages initially
- RTL language support (Arabic, Hebrew)
- Cultural localization of content
- Regional pricing and payment methods
- Timezone-aware scheduling

## Development Guidelines

### Code Quality Standards
- Follow PEP 8 for Python code
- Use TypeScript strict mode
- Implement comprehensive error handling
- Add detailed logging and monitoring
- Write self-documenting code with clear comments

### API Design Principles
- RESTful API design with OpenAPI documentation
- Consistent error response format
- Proper HTTP status codes
- API versioning strategy
- Rate limiting and authentication

### Frontend Best Practices
- Component-based architecture
- Responsive design (mobile-first)
- Accessibility best practices
- Performance optimization
- Progressive enhancement

## Deployment & DevOps

### Local Development Setup
```bash
# Quick start commands
git clone <repo>
cd eduai-global
docker-compose up -d  # Start all services
npm run dev          # Start development servers
```

### CI/CD Pipeline
- Automated testing on pull requests
- Code quality checks (linting, type checking)
- Security scanning
- Automated deployment to staging
- Production deployment with approval gates

## Success Metrics
- 85% course completion rate
- < 2s page load times
- 99.9% uptime
- Support for 1M+ concurrent users
- Multi-language content delivery
- Offline functionality for mobile users

## Detailed User Stories Implementation

### Phase 1 (MVP) - Critical Features
**User Registration & Authentication (US-001 to US-004)**
- Implement email/password registration with validation
- Add social media login (Google, LinkedIn, GitHub)
- Create user profile setup with language/timezone selection
- Build initial AI assessment quiz for skill level determination

**Core Learning Experience (US-016, US-017, US-059)**
- Develop video player with progress tracking
- Implement offline content download for mobile
- Create progress dashboard with analytics
- Build basic gamification (streaks, achievements)

**Course Management (US-009 to US-011)**
- Design adaptive learning path algorithm
- Implement course catalog with filtering
- Create enrollment and progress tracking system
- Build skill assessment and testing-out functionality

### Phase 2 - Advanced Features
**AI Personalization (US-012 to US-015)**
- Integrate OpenAI API for content recommendations
- Implement bite-sized learning for professionals
- Create business-focused case studies
- Build ROI calculators for entrepreneurs

**Community Features (US-031 to US-037)**
- Develop study group matching system
- Implement mentorship booking platform
- Create discussion forums with real-time messaging
- Build networking features for professionals

**Mobile & PWA (US-049 to US-053)**
- Implement Progressive Web App functionality
- Add push notifications for learning reminders
- Create offline sync mechanism
- Optimize for low-bandwidth environments

### Phase 3 - Scale Features
**Corporate Training (US-043 to US-048)**
- Build admin dashboard for corporate accounts
- Implement bulk user management
- Create custom learning path creation tools
- Add comprehensive analytics and reporting

**Advanced Analytics (US-060 to US-063)**
- Implement detailed learning analytics
- Create peer comparison features
- Build educator analytics dashboard
- Add data export functionality

## Technical Implementation Details

### Authentication Flow
```python
# Example FastAPI authentication endpoint
@router.post("/auth/register", response_model=UserResponse)
async def register_user(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    # Validate email uniqueness
    # Hash password with bcrypt
    # Create user record
    # Generate JWT token
    # Send welcome email
    return {"user": user, "token": jwt_token}
```

### AI Recommendation Service
```python
# Example AI service integration
class AIRecommendationService:
    def __init__(self):
        self.openai_client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.pinecone_client = Pinecone(api_key=settings.PINECONE_API_KEY)

    async def get_personalized_recommendations(self, user_id: str):
        # Fetch user learning history
        # Generate embeddings for user preferences
        # Query vector database for similar content
        # Use OpenAI to rank and explain recommendations
        return recommendations
```

### React Component Structure
```typescript
// Example React component with TypeScript
interface CourseCardProps {
  course: Course;
  onEnroll: (courseId: string) => void;
  userProgress?: UserProgress;
}

const CourseCard: React.FC<CourseCardProps> = ({
  course,
  onEnroll,
  userProgress
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <Card sx={{ maxWidth: 345, margin: 2 }}>
      {/* Course content with MUI components */}
    </Card>
  );
};
```

## Database Relationships & Indexes

### Key Relationships
```sql
-- Foreign key relationships
ALTER TABLE user_progress ADD CONSTRAINT fk_user_progress_user
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE user_progress ADD CONSTRAINT fk_user_progress_course
  FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE;

-- Indexes for performance
CREATE INDEX idx_user_progress_user_id ON user_progress(user_id);
CREATE INDEX idx_user_progress_course_id ON user_progress(course_id);
CREATE INDEX idx_courses_tags ON courses USING GIN(tags);
CREATE INDEX idx_courses_title_gin ON courses USING GIN(title);
```

### JSONB Queries for Multi-language Content
```sql
-- Query courses by language
SELECT * FROM courses
WHERE title->>'en' ILIKE '%machine learning%'
   OR title->>'es' ILIKE '%aprendizaje automático%';

-- Update multi-language content
UPDATE courses
SET title = jsonb_set(title, '{fr}', '"Apprentissage Automatique"')
WHERE id = 'course-uuid';
```

## Security Implementation

### JWT Token Management
```python
# JWT token creation and validation
def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(token: str = Depends(oauth2_scheme)):
    # Validate and decode JWT token
    # Return user object or raise authentication error
    pass
```

### Input Validation with Pydantic
```python
class UserCreate(BaseModel):
    email: EmailStr
    name: str = Field(..., min_length=2, max_length=100)
    password: str = Field(..., min_length=8)
    preferred_language: str = Field(default="en", regex="^[a-z]{2}$")

    @validator('password')
    def validate_password(cls, v):
        # Password strength validation
        if not re.search(r"[A-Z]", v):
            raise ValueError('Password must contain uppercase letter')
        return v
```

## Performance Optimization

### Caching Strategy
```python
# Redis caching for frequently accessed data
@cache(expire=3600)  # Cache for 1 hour
async def get_course_recommendations(user_id: str):
    # Expensive AI computation
    return recommendations

# Database query optimization
async def get_user_courses_with_progress(user_id: str):
    query = select(Course, UserProgress).join(
        UserProgress, Course.id == UserProgress.course_id
    ).where(UserProgress.user_id == user_id)

    return await db.execute(query)
```

### Frontend Performance
```typescript
// Code splitting with React.lazy
const CoursePage = lazy(() => import('./pages/CoursePage'));
const DashboardPage = lazy(() => import('./pages/DashboardPage'));

// Memoization for expensive computations
const MemoizedCourseList = memo(({ courses, filters }) => {
  const filteredCourses = useMemo(() =>
    courses.filter(course => matchesFilters(course, filters)),
    [courses, filters]
  );

  return <CourseGrid courses={filteredCourses} />;
});
```

## Testing Implementation

### Backend Testing
```python
# pytest example for API testing
@pytest.mark.asyncio
async def test_user_registration(client: AsyncClient):
    user_data = {
        "email": "<EMAIL>",
        "name": "Test User",
        "password": "SecurePass123",
        "preferred_language": "en"
    }

    response = await client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 201
    assert "token" in response.json()
```

### Frontend Testing
```typescript
// React Testing Library example
import { render, screen, fireEvent } from '@testing-library/react';
import { LoginForm } from './LoginForm';

test('displays validation error for invalid email', async () => {
  render(<LoginForm />);

  const emailInput = screen.getByLabelText(/email/i);
  const submitButton = screen.getByRole('button', { name: /login/i });

  fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
  fireEvent.click(submitButton);

  expect(await screen.findByText(/invalid email format/i)).toBeInTheDocument();
});
```

## Additional Context
This platform aims to bridge the global AI education gap by making high-quality AI learning accessible to everyone, regardless of location or economic status. The solution should be production-ready, scalable, and designed for global deployment from day one.

**Please generate a complete, production-ready codebase that implements all the above requirements, following modern development best practices and ensuring the platform can scale to serve millions of users globally.**

## Final Implementation Notes

### Priority Implementation Order
1. **Week 1-2**: Basic authentication, user management, database setup
2. **Week 3-4**: Course catalog, enrollment system, basic progress tracking
3. **Week 5-6**: Video player, content delivery, mobile responsiveness
4. **Week 7-8**: AI recommendations, basic community features
5. **Week 9-10**: Payment integration, subscription management
6. **Week 11-12**: PWA features, offline capability, testing, deployment

### Success Criteria
- All P0 user stories implemented and tested
- Platform can handle 10,000+ concurrent users
- Mobile-first responsive design
- Multi-language support for at least 5 languages
- Comprehensive test coverage (>80%)
- Production deployment with monitoring
- Security audit passed
- Performance benchmarks met

This comprehensive prompt provides everything needed to build a world-class AI education platform that can democratize AI learning globally.
