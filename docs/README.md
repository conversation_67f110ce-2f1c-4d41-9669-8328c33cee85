# 🚀 EduAI Global Academy Hub - Setup & Running Guide

This guide provides step-by-step instructions for setting up and running the EduAI Global Academy Hub monorepo locally.

## 📋 Prerequisites

Before you begin, ensure you have the following installed on your system:

### Required Software
- **Node.js 18+** - [Download from nodejs.org](https://nodejs.org/)
- **npm 9+** (comes with Node.js)
- **Git** - [Download from git-scm.com](https://git-scm.com/)
- **Docker & Docker Compose** - [Download from docker.com](https://www.docker.com/get-started/)

### Optional (for local database development)
- **PostgreSQL 15+** - [Download from postgresql.org](https://www.postgresql.org/download/)
- **Redis 7+** - [Download from redis.io](https://redis.io/download/)

### Verify Installation
```bash
# Check Node.js version (should be 18+)
node --version

# Check npm version (should be 9+)
npm --version

# Check Docker version
docker --version
docker-compose --version

# Check Git version
git --version
```

## 🏗️ Project Setup

### 1. Clone the Repository
```bash
# Clone the repository
git clone <your-repository-url>
cd eduai-global-academy-hub

# Verify you're in the correct directory
ls -la
# You should see: apps/, packages/, docs/, scripts/, etc.
```

### 2. Automated Setup (Recommended)
```bash
# Make the setup script executable
chmod +x scripts/setup.sh

# Run the automated setup
./scripts/setup.sh
```

The setup script will:
- ✅ Check Node.js version compatibility
- ✅ Install all dependencies for all packages
- ✅ Create `.env` file from template
- ✅ Build the shared package
- ✅ Check database connectivity
- ✅ Create necessary directories

### 3. Manual Setup (Alternative)
If you prefer manual setup or the script fails:

```bash
# Install root dependencies
npm install

# Install workspace dependencies
npm run install:all

# Create environment file
cp .env.example .env

# Build shared package
npm run build:shared

# Create logs directory
mkdir -p logs
```

## ⚙️ Environment Configuration

### 1. Edit Environment Variables
Open the `.env` file and update the following values:

```bash
# Open .env file in your preferred editor
nano .env
# or
code .env
# or
vim .env
```

### 2. Required Environment Variables
```env
# Application Settings
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_URL=postgresql://eduai_user:eduai_password@localhost:5432/eduai_db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Security (IMPORTANT: Change in production!)
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Configuration
FRONTEND_URL=http://localhost:5173

# API Configuration
API_BASE_URL=http://localhost:3001

# Logging
LOG_LEVEL=info
```

### 3. Security Notes
- 🔒 **Never commit `.env` files to version control**
- 🔑 **Use strong, unique JWT secrets in production**
- 🔄 **Rotate secrets regularly**
- 🌍 **Use environment-specific configurations**

## 🗄️ Database Setup

You have two options for running the databases:

### Option A: Docker (Recommended)
```bash
# Start PostgreSQL and Redis with Docker
docker-compose up postgres redis -d

# Verify services are running
docker ps

# Check logs if needed
docker-compose logs postgres
docker-compose logs redis
```

### Option B: Local Installation
If you have PostgreSQL and Redis installed locally:

```bash
# Start PostgreSQL (varies by OS)
# macOS with Homebrew:
brew services start postgresql

# Ubuntu/Debian:
sudo systemctl start postgresql

# Start Redis (varies by OS)
# macOS with Homebrew:
brew services start redis

# Ubuntu/Debian:
sudo systemctl start redis-server

# Create database
createdb eduai_db
```

### Database Verification
```bash
# Test PostgreSQL connection
docker exec -it eduai-postgres psql -U eduai_user -d eduai_db -c "SELECT version();"

# Test Redis connection
docker exec -it eduai-redis redis-cli ping
# Should return: PONG
```

## 🚀 Running the Application

### Development Mode

#### Option 1: Start Everything Together
```bash
# Start both frontend and backend
npm run dev
```

This will start:
- 🎨 **Frontend** on http://localhost:5173
- 🔧 **Backend API** on http://localhost:3001

#### Option 2: Start Services Separately
```bash
# Terminal 1: Start backend only
npm run dev:backend

# Terminal 2: Start frontend only
npm run dev:frontend
```

#### Option 3: Full Docker Development
```bash
# Start all services with Docker (including hot reload)
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

### Production Mode
```bash
# Build all packages
npm run build

# Start with Docker Compose
docker-compose up -d

# Or start built applications directly
cd apps/backend && npm start &
cd apps/frontend && npm run preview
```

## 🌐 Accessing the Application

Once running, you can access:

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | http://localhost:5173 | React application |
| **Backend API** | http://localhost:3001 | Express.js API |
| **API Health Check** | http://localhost:3001/health | Health status |
| **PostgreSQL** | localhost:5432 | Database (if using Docker) |
| **Redis** | localhost:6379 | Cache (if using Docker) |

### API Endpoints
- `GET /health` - Health check
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `GET /api/v1/courses` - List courses
- `GET /api/v1/community/posts` - Community posts

## 🧪 Testing

### Run All Tests
```bash
# Run all tests across all packages
npm run test
```

### Run Specific Tests
```bash
# Backend tests only
npm run test:backend

# Frontend tests only
npm run test:frontend

# Shared package tests only
npm run test:shared

# End-to-end tests
npm run test:e2e
```

### Test with Coverage
```bash
# Backend with coverage
cd apps/backend && npm run test:coverage

# Frontend with coverage
cd apps/frontend && npm run test -- --coverage
```

### E2E Testing Setup
```bash
# Install Playwright browsers (first time only)
cd apps/frontend
npx playwright install

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui
```

## 🔧 Development Commands

### Building
```bash
# Build all packages
npm run build

# Build specific packages
npm run build:shared
npm run build:backend
npm run build:frontend
```

### Linting & Formatting
```bash
# Lint all code
npm run lint

# Format all code
npm run format

# Lint specific packages
npm run lint:backend
npm run lint:frontend
npm run lint:shared
```

### Cleaning
```bash
# Clean all build artifacts
npm run clean

# Clean specific packages
npm run clean:backend
npm run clean:frontend
npm run clean:shared
```

## 🐳 Docker Commands

### Development
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop development environment
docker-compose -f docker-compose.dev.yml down
```

### Production
```bash
# Start production environment
docker-compose up -d

# View logs
docker-compose logs -f

# Stop production environment
docker-compose down

# Rebuild images
docker-compose build --no-cache
```

### Database Management
```bash
# Connect to PostgreSQL
docker exec -it eduai-postgres psql -U eduai_user -d eduai_db

# Connect to Redis
docker exec -it eduai-redis redis-cli

# Backup database
docker exec eduai-postgres pg_dump -U eduai_user eduai_db > backup.sql

# Restore database
docker exec -i eduai-postgres psql -U eduai_user -d eduai_db < backup.sql
```

## 🔍 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port 3001 (backend)
lsof -i :3001
kill -9 <PID>

# Find process using port 5173 (frontend)
lsof -i :5173
kill -9 <PID>
```

#### Database Connection Issues
```bash
# Check if containers are running
docker ps

# Restart database services
docker-compose restart postgres redis

# Check database logs
docker-compose logs postgres

# Reset database (WARNING: This will delete all data)
docker-compose down
docker volume rm eduai-global-academy-hub_postgres_data
docker-compose up postgres -d
```

#### Node Modules Issues
```bash
# Clean and reinstall dependencies
npm run clean
rm -rf node_modules package-lock.json
npm install
```

#### TypeScript Build Issues
```bash
# Clean TypeScript cache
npx tsc --build --clean

# Rebuild shared package
npm run build:shared

# Check for type errors
npm run lint
```

#### Docker Issues
```bash
# Clean Docker system
docker system prune -a

# Rebuild containers
docker-compose build --no-cache

# Check Docker logs
docker-compose logs
```

### Performance Tips

1. **Keep Docker containers running** to avoid startup time
2. **Use `--watch` flags** for continuous testing during development
3. **Enable hot reload** by using `npm run dev`
4. **Monitor resource usage** with `docker stats`

### Getting Help

If you encounter issues:

1. **Check the logs** - Most issues are visible in application logs
2. **Verify environment variables** - Ensure `.env` is properly configured
3. **Check service status** - Ensure all required services are running
4. **Review documentation** - Check API docs and component guides
5. **Search existing issues** - Look for similar problems in the repository
6. **Create an issue** - Provide detailed reproduction steps

## 📚 Next Steps

After successful setup:

1. **Explore the codebase** - Familiarize yourself with the project structure
2. **Read the API documentation** - Understand available endpoints
3. **Check out the frontend components** - Explore the React components
4. **Run the tests** - Ensure everything works correctly
5. **Start developing** - Begin adding your features!

---

**Happy coding! 🚀**
