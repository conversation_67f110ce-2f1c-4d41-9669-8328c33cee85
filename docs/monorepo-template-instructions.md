# EduAI Global Academy Hub - Node.js Monorepo

A modern, production-ready monorepo for the EduAI Global Academy Hub built with Node.js, React, TypeScript, and modern development tools. This monorepo contains a full-stack application with a Node.js/Express backend, React/TypeScript frontend, and shared TypeScript packages.

## 🏗️ Architecture Overview

This monorepo follows a modern architecture pattern with:

- **Backend**: Node.js + Express + TypeScript
- **Frontend**: React + TypeScript + Vite + ShadCN UI
- **Shared**: Common TypeScript types, utilities, and constants
- **Database**: PostgreSQL with Redis for caching
- **Containerization**: Docker and Docker Compose
- **CI/CD**: GitHub Actions
- **Testing**: Je<PERSON> (backend), <PERSON><PERSON><PERSON> (frontend), <PERSON><PERSON> (E2E)

## 📋 Prerequisites

Ensure the following are installed on your system:

- **Node.js 18+** - [Download](https://nodejs.org/)
- **npm 9+** (comes with Node.js)
- **Docker & Docker Compose** - [Download](https://www.docker.com/get-started)
- **Git** - [Download](https://git-scm.com/)

### Optional but Recommended:
- **PostgreSQL** (for local development without Docker)
- **Redis** (for local development without Docker)

## 🚀 Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd eduai-global-academy-hub

# Run the setup script
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit the .env file with your configuration
# Update database URLs, JWT secrets, etc.
```

### 3. Start Development Environment

#### Option A: Using Docker (Recommended)
```bash
# Start all services
docker-compose up -d

# Or start only database services
docker-compose up postgres redis -d

# Then start development servers
npm run dev
```

#### Option B: Local Development
```bash
# Start database services with Docker
docker-compose up postgres redis -d

# Start development servers
npm run dev
```

## 📁 Project Structure

```
eduai-global-academy-hub/
├── apps/
│   ├── backend/                 # Node.js/Express API
│   │   ├── src/
│   │   │   ├── controllers/     # Route controllers
│   │   │   ├── middleware/      # Express middleware
│   │   │   ├── routes/          # API routes
│   │   │   ├── services/        # Business logic
│   │   │   ├── utils/           # Utility functions
│   │   │   └── index.ts         # Application entry point
│   │   ├── tests/               # Backend tests
│   │   ├── Dockerfile           # Production Docker image
│   │   ├── Dockerfile.dev       # Development Docker image
│   │   └── package.json
│   └── frontend/                # React/TypeScript app
│       ├── src/
│       │   ├── components/      # React components
│       │   ├── pages/           # Page components
│       │   ├── hooks/           # Custom React hooks
│       │   ├── lib/             # Utility libraries
│       │   └── main.tsx         # Application entry point
│       ├── public/              # Static assets
│       ├── tests/               # Frontend tests
│       ├── Dockerfile           # Production Docker image
│       ├── Dockerfile.dev       # Development Docker image
│       └── package.json
├── packages/
│   └── shared/                  # Shared TypeScript package
│       ├── src/
│       │   ├── types/           # TypeScript type definitions
│       │   ├── utils/           # Shared utilities
│       │   ├── constants/       # Application constants
│       │   └── index.ts         # Package entry point
│       └── package.json
├── infrastructure/
│   └── postgres/
│       └── init.sql             # Database initialization
├── scripts/
│   └── setup.sh                # Development setup script
├── .github/
│   └── workflows/               # GitHub Actions CI/CD
├── docker-compose.yml           # Production Docker Compose
├── docker-compose.dev.yml       # Development Docker Compose
├── .env.example                 # Environment template
└── package.json                 # Root package.json with workspaces
```

## 🛠️ Development

### Available Scripts

#### Root Level Commands
```bash
# Development
npm run dev                    # Start both frontend and backend
npm run dev:frontend          # Start only frontend (port 5173)
npm run dev:backend           # Start only backend (port 3001)

# Building
npm run build                 # Build all packages
npm run build:shared          # Build shared package
npm run build:backend         # Build backend
npm run build:frontend        # Build frontend

# Testing
npm run test                  # Run all tests
npm run test:shared           # Test shared package
npm run test:backend          # Test backend
npm run test:frontend         # Test frontend
npm run test:e2e              # Run E2E tests

# Linting & Formatting
npm run lint                  # Lint all packages
npm run format                # Format all packages

# Cleanup
npm run clean                 # Clean all build artifacts
```

#### Backend-Specific Commands
```bash
cd apps/backend

npm run dev                   # Start development server with hot reload
npm run build                 # Build TypeScript to JavaScript
npm run start                 # Start production server
npm run test                  # Run Jest tests
npm run test:watch            # Run tests in watch mode
npm run test:coverage         # Run tests with coverage
npm run lint                  # ESLint
npm run format                # Prettier
```

#### Frontend-Specific Commands
```bash
cd apps/frontend

npm run dev                   # Start Vite development server
npm run build                 # Build for production
npm run preview               # Preview production build
npm run test                  # Run Vitest tests
npm run test:ui               # Run tests with UI
npm run test:e2e              # Run Playwright E2E tests
npm run lint                  # ESLint
npm run format                # Prettier
```

### 🐳 Docker Usage

#### Development with Docker
```bash
# Start all services for development
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

#### Production with Docker
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild images
docker-compose build --no-cache
```

### 🗄️ Database Management

#### Using Docker (Recommended)
```bash
# Start PostgreSQL and Redis
docker-compose up postgres redis -d

# Connect to PostgreSQL
docker exec -it eduai-postgres psql -U eduai_user -d eduai_db

# Connect to Redis
docker exec -it eduai-redis redis-cli
```

#### Local Installation
If you prefer local installations:

**PostgreSQL:**
```bash
# macOS
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib
sudo systemctl start postgresql

# Create database
createdb eduai_db
```

**Redis:**
```bash
# macOS
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server
```

### 🧪 Testing

#### Backend Testing (Jest)
```bash
cd apps/backend

# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- auth.test.ts
```

#### Frontend Testing (Vitest)
```bash
cd apps/frontend

# Run all tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests in watch mode
npm run test -- --watch
```

#### E2E Testing (Playwright)
```bash
cd apps/frontend

# Install Playwright browsers (first time only)
npx playwright install

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run specific test
npx playwright test login.spec.ts
```

### 🔧 Configuration

#### Environment Variables
The application uses environment variables for configuration. Copy `.env.example` to `.env` and update the values:

```bash
# Application
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL=postgresql://eduai_user:eduai_password@localhost:5432/eduai_db

# Redis
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173
```

### 📦 Package Management

This monorepo uses npm workspaces for package management. The root `package.json` defines workspaces for:

- `apps/*` - Applications (frontend, backend)
- `packages/*` - Shared packages

#### Adding Dependencies

**To a specific workspace:**
```bash
# Add to backend
npm install express --workspace=apps/backend

# Add to frontend
npm install axios --workspace=apps/frontend

# Add to shared package
npm install zod --workspace=packages/shared
```

**To root (affects all workspaces):**
```bash
npm install typescript --save-dev
```

#### Workspace Commands
```bash
# Run command in specific workspace
npm run build --workspace=apps/backend

# Run command in all workspaces
npm run test --workspaces

# Install dependencies for all workspaces
npm install
```

### 🔄 API Integration

The frontend is configured to work with the backend API. Key integration points:

#### API Client Setup
The frontend uses axios for API calls with a configured base client:

```typescript
// In apps/frontend/src/lib/api.ts
import axios from 'axios';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  timeout: 10000,
});

export default api;
```

#### Shared Types
Both frontend and backend use shared TypeScript types from `@eduai/shared`:

```typescript
import { User, Course, ApiResponse } from '@eduai/shared';
```

### 🚀 Deployment

#### Production Build
```bash
# Build all packages for production
npm run build

# Build specific package
npm run build:backend
npm run build:frontend
```

#### Docker Deployment
```bash
# Build and deploy with Docker Compose
docker-compose up -d

# Or build images separately
docker build -f apps/backend/Dockerfile -t eduai-backend .
docker build -f apps/frontend/Dockerfile -t eduai-frontend .
```

#### Environment-Specific Deployments
```bash
# Development environment
docker-compose -f docker-compose.dev.yml up -d

# Production environment
docker-compose -f docker-compose.yml up -d
```

### 🔒 Security Considerations

#### Environment Variables
- Never commit `.env` files to version control
- Use strong, unique JWT secrets in production
- Rotate secrets regularly
- Use environment-specific configurations

#### Database Security
- Use strong database passwords
- Enable SSL/TLS for database connections in production
- Regularly update database software
- Implement proper backup strategies

#### API Security
- Rate limiting is configured by default
- CORS is properly configured
- Input validation using Zod schemas
- JWT token expiration is enforced

### 🔧 Troubleshooting

#### Common Issues

**Port Already in Use:**
```bash
# Find process using port 3001 (backend)
lsof -i :3001
kill -9 <PID>

# Find process using port 5173 (frontend)
lsof -i :5173
kill -9 <PID>
```

**Database Connection Issues:**
```bash
# Check if PostgreSQL is running
docker ps | grep postgres

# Check PostgreSQL logs
docker logs eduai-postgres

# Reset database
docker-compose down
docker volume rm eduai-global-academy-hub_postgres_data
docker-compose up postgres -d
```

**Node Modules Issues:**
```bash
# Clean and reinstall
npm run clean
rm -rf node_modules package-lock.json
npm install
```

**TypeScript Build Issues:**
```bash
# Clean TypeScript cache
npx tsc --build --clean

# Rebuild shared package
npm run build:shared
```

#### Performance Tips

1. **Development:**
   - Use `npm run dev` for hot reloading
   - Keep Docker containers running to avoid startup time
   - Use `--watch` flags for continuous testing

2. **Production:**
   - Build with `NODE_ENV=production`
   - Use Docker multi-stage builds for smaller images
   - Enable gzip compression in nginx
   - Use Redis for caching

3. **Database:**
   - Use connection pooling
   - Add appropriate indexes
   - Monitor query performance
   - Regular database maintenance

### 📚 API Documentation

The backend API provides the following endpoints:

#### Authentication (`/api/v1/auth`)
- `POST /login` - User login
- `POST /register` - User registration
- `POST /logout` - User logout
- `GET /me` - Get current user profile

#### Courses (`/api/v1/courses`)
- `GET /` - List courses (with filtering and pagination)
- `GET /:id` - Get course details
- `GET /featured` - Get featured courses

#### Users (`/api/v1/users`)
- `GET /profile` - Get user profile
- `PUT /profile` - Update user profile
- `GET /enrolled-courses` - Get user's enrolled courses
- `GET /achievements` - Get user achievements

#### Community (`/api/v1/community`)
- `GET /posts` - List community posts
- `POST /posts` - Create new post
- `GET /study-groups` - List study groups

#### Health Check
- `GET /health` - Application health status

### 🎨 Frontend Features

The React frontend includes:

#### Pages
- **Home** (`/`) - Landing page with featured courses and stats
- **Courses** (`/courses`) - Course catalog with search and filtering
- **Community** (`/community`) - Community posts and study groups
- **About** (`/about`) - About page

#### Components
- **UI Components** - Built with ShadCN UI and Radix UI
- **Forms** - React Hook Form with Zod validation
- **Navigation** - Responsive navigation with mobile menu
- **Cards** - Course cards, post cards, user cards
- **Modals** - Dialog components for various actions

#### Features
- **Responsive Design** - Mobile-first approach
- **Dark/Light Mode** - Theme switching capability
- **Search & Filtering** - Advanced course and content filtering
- **Authentication** - Login/register forms with validation
- **State Management** - React Query for server state
- **Routing** - React Router for navigation

### 🔄 CI/CD Pipeline

The project includes GitHub Actions workflows for:

#### Continuous Integration (`.github/workflows/ci.yml`)
- **Dependency Installation** - Caches node_modules for faster builds
- **Linting** - ESLint for all packages
- **Building** - TypeScript compilation and Vite builds
- **Testing** - Jest (backend), Vitest (frontend), Playwright (E2E)
- **Security Audit** - npm audit for vulnerabilities

#### Docker Builds (`.github/workflows/docker.yml`)
- **Multi-platform Builds** - AMD64 and ARM64 support
- **Container Registry** - Pushes to GitHub Container Registry
- **Security Scanning** - Trivy vulnerability scanning
- **Automated Tagging** - Semantic versioning support

#### Deployment Stages
1. **Development** - Triggered on `develop` branch
2. **Staging** - Triggered on `develop` branch push
3. **Production** - Triggered on `main` branch push

### 🛡️ Code Quality

#### Linting & Formatting
- **ESLint** - JavaScript/TypeScript linting
- **Prettier** - Code formatting
- **TypeScript** - Type checking
- **Husky** - Git hooks (optional)

#### Testing Strategy
- **Unit Tests** - Jest for backend logic
- **Component Tests** - Vitest + Testing Library for React
- **Integration Tests** - API endpoint testing
- **E2E Tests** - Playwright for user workflows

#### Code Standards
- **TypeScript Strict Mode** - Enabled for all packages
- **Shared Types** - Common types in `@eduai/shared`
- **API Validation** - Zod schemas for request/response validation
- **Error Handling** - Consistent error responses

### 🌍 Environment Management

#### Development Environment
```bash
# Local development with hot reload
npm run dev

# Development with Docker
docker-compose -f docker-compose.dev.yml up -d
```

#### Staging Environment
```bash
# Build for staging
NODE_ENV=staging npm run build

# Deploy to staging
docker-compose -f docker-compose.staging.yml up -d
```

#### Production Environment
```bash
# Build for production
NODE_ENV=production npm run build

# Deploy to production
docker-compose up -d
```

### 📊 Monitoring & Logging

#### Application Logs
- **Backend** - Winston logger with structured logging
- **Frontend** - Console logging in development, structured in production
- **Docker** - Centralized logging with Docker Compose

#### Health Checks
- **Backend** - `/health` endpoint with service status
- **Frontend** - Nginx health check endpoint
- **Database** - PostgreSQL and Redis health checks

#### Metrics (Future Enhancement)
- Application performance monitoring
- Database query performance
- API response times
- User analytics

### 🤝 Contributing

#### Development Workflow
1. **Fork & Clone** - Fork the repository and clone locally
2. **Branch** - Create a feature branch from `develop`
3. **Develop** - Make changes with tests
4. **Test** - Run all tests and linting
5. **Commit** - Use conventional commit messages
6. **Push** - Push to your fork
7. **PR** - Create pull request to `develop`

#### Commit Message Format
```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

#### Code Review Process
- All PRs require review
- CI/CD must pass
- Tests must be included for new features
- Documentation updates for API changes

### 📋 Roadmap

#### Phase 1 - Core Platform ✅
- [x] User authentication and profiles
- [x] Course catalog and enrollment
- [x] Community features (posts, study groups)
- [x] Basic admin functionality

#### Phase 2 - Enhanced Learning 🚧
- [ ] Video streaming and progress tracking
- [ ] Interactive coding exercises
- [ ] Real-time collaboration tools
- [ ] Advanced search and recommendations

#### Phase 3 - AI Integration 📋
- [ ] AI-powered learning paths
- [ ] Intelligent tutoring system
- [ ] Automated content generation
- [ ] Predictive analytics

### 🆘 Support & Resources

#### Documentation
- **API Docs** - Available at `/api/docs` when backend is running
- **Component Storybook** - `npm run storybook` (if configured)
- **Database Schema** - See `infrastructure/postgres/init.sql`

#### Community
- **GitHub Issues** - Bug reports and feature requests
- **Discussions** - General questions and ideas
- **Wiki** - Additional documentation and guides

#### Getting Help
1. **Check Documentation** - Start with this README and API docs
2. **Search Issues** - Look for existing solutions
3. **Create Issue** - Provide detailed reproduction steps
4. **Join Discussions** - Ask questions in GitHub Discussions

### 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### 🙏 Acknowledgments

- **React Team** - For the amazing React framework
- **Vite Team** - For the fast build tool
- **ShadCN** - For the beautiful UI components
- **Radix UI** - For accessible component primitives
- **TypeScript Team** - For type safety
- **Node.js Community** - For the runtime and ecosystem

---

## 📞 Contact

- **Project Maintainer**: [Your Name](mailto:<EMAIL>)
- **Project Repository**: [GitHub](https://github.com/your-username/eduai-global-academy-hub)
- **Documentation**: [Wiki](https://github.com/your-username/eduai-global-academy-hub/wiki)

---

**Happy Coding! 🚀**

---

## 🎯 Quick Reference

### Essential Commands
```bash
# Setup
./scripts/setup.sh

# Development
npm run dev

# Testing
npm run test

# Building
npm run build

# Docker
docker-compose up -d
```

### Key URLs (Development)
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **API Health**: http://localhost:3001/health
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

### Project Highlights
- ✅ **Full-Stack TypeScript** - End-to-end type safety
- ✅ **Modern React** - Hooks, Context, React Query
- ✅ **Express.js Backend** - RESTful API with middleware
- ✅ **PostgreSQL Database** - Relational data with migrations
- ✅ **Redis Caching** - Performance optimization
- ✅ **Docker Ready** - Development and production containers
- ✅ **CI/CD Pipeline** - GitHub Actions automation
- ✅ **Testing Suite** - Unit, integration, and E2E tests
- ✅ **Code Quality** - Linting, formatting, type checking
- ✅ **Monorepo Structure** - Organized, scalable codebase

This monorepo provides a production-ready foundation for building modern web applications with Node.js and React, following industry best practices and conventions.
