# EduAI Global Academy Hub - Node.js Monorepo

A modern, production-ready monorepo for the EduAI Global Academy Hub built with Node.js, React, TypeScript, and modern development tools. This monorepo contains a full-stack application with a Node.js/Express backend, React/TypeScript frontend, and shared TypeScript packages.

## 🏗️ Architecture Overview

This monorepo follows a modern architecture pattern with:

- **Backend**: Node.js + Express + TypeScript
- **Frontend**: React + TypeScript + Vite + ShadCN UI
- **Shared**: Common TypeScript types, utilities, and constants
- **Database**: PostgreSQL with Redis for caching
- **Containerization**: Docker and Docker Compose
- **CI/CD**: GitHub Actions
- **Testing**: Je<PERSON> (backend), <PERSON><PERSON><PERSON> (frontend), <PERSON><PERSON> (E2E)

## 📋 Prerequisites

Ensure the following are installed on your system:

- **Node.js 18+** - [Download](https://nodejs.org/)
- **npm 9+** (comes with Node.js)
- **Docker & Docker Compose** - [Download](https://www.docker.com/get-started)
- **Git** - [Download](https://git-scm.com/)

### Optional but Recommended:
- **PostgreSQL** (for local development without Docker)
- **Redis** (for local development without Docker)

## 🚀 Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd eduai-global-academy-hub

# Run the setup script
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit the .env file with your configuration
# Update database URLs, JWT secrets, etc.
```

### 3. Start Development Environment

#### Option A: Using Docker (Recommended)
```bash
# Start all services
docker-compose up -d

# Or start only database services
docker-compose up postgres redis -d

# Then start development servers
npm run dev
```

#### Option B: Local Development
```bash
# Start database services with Docker
docker-compose up postgres redis -d

# Start development servers
npm run dev
```

## 📁 Project Structure

```
eduai-global-academy-hub/
├── apps/
│   ├── backend/                 # Node.js/Express API
│   │   ├── src/
│   │   │   ├── controllers/     # Route controllers
│   │   │   ├── middleware/      # Express middleware
│   │   │   ├── routes/          # API routes
│   │   │   ├── services/        # Business logic
│   │   │   ├── utils/           # Utility functions
│   │   │   └── index.ts         # Application entry point
│   │   ├── tests/               # Backend tests
│   │   ├── Dockerfile           # Production Docker image
│   │   ├── Dockerfile.dev       # Development Docker image
│   │   └── package.json
│   └── frontend/                # React/TypeScript app
│       ├── src/
│       │   ├── components/      # React components
│       │   ├── pages/           # Page components
│       │   ├── hooks/           # Custom React hooks
│       │   ├── lib/             # Utility libraries
│       │   └── main.tsx         # Application entry point
│       ├── public/              # Static assets
│       ├── tests/               # Frontend tests
│       ├── Dockerfile           # Production Docker image
│       ├── Dockerfile.dev       # Development Docker image
│       └── package.json
├── packages/
│   └── shared/                  # Shared TypeScript package
│       ├── src/
│       │   ├── types/           # TypeScript type definitions
│       │   ├── utils/           # Shared utilities
│       │   ├── constants/       # Application constants
│       │   └── index.ts         # Package entry point
│       └── package.json
├── infrastructure/
│   └── postgres/
│       └── init.sql             # Database initialization
├── scripts/
│   └── setup.sh                # Development setup script
├── .github/
│   └── workflows/               # GitHub Actions CI/CD
├── docker-compose.yml           # Production Docker Compose
├── docker-compose.dev.yml       # Development Docker Compose
├── .env.example                 # Environment template
└── package.json                 # Root package.json with workspaces
```

## 🛠️ Development

### Available Scripts

#### Root Level Commands
```bash
# Development
npm run dev                    # Start both frontend and backend
npm run dev:frontend          # Start only frontend (port 5173)
npm run dev:backend           # Start only backend (port 3001)

# Building
npm run build                 # Build all packages
npm run build:shared          # Build shared package
npm run build:backend         # Build backend
npm run build:frontend        # Build frontend

# Testing
npm run test                  # Run all tests
npm run test:shared           # Test shared package
npm run test:backend          # Test backend
npm run test:frontend         # Test frontend
npm run test:e2e              # Run E2E tests

# Linting & Formatting
npm run lint                  # Lint all packages
npm run format                # Format all packages

# Cleanup
npm run clean                 # Clean all build artifacts
```

#### Backend-Specific Commands
```bash
cd apps/backend

npm run dev                   # Start development server with hot reload
npm run build                 # Build TypeScript to JavaScript
npm run start                 # Start production server
npm run test                  # Run Jest tests
npm run test:watch            # Run tests in watch mode
npm run test:coverage         # Run tests with coverage
npm run lint                  # ESLint
npm run format                # Prettier
```

#### Frontend-Specific Commands
```bash
cd apps/frontend

npm run dev                   # Start Vite development server
npm run build                 # Build for production
npm run preview               # Preview production build
npm run test                  # Run Vitest tests
npm run test:ui               # Run tests with UI
npm run test:e2e              # Run Playwright E2E tests
npm run lint                  # ESLint
npm run format                # Prettier
```

### 🐳 Docker Usage

#### Development with Docker
```bash
# Start all services for development
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f

# Stop services
docker-compose -f docker-compose.dev.yml down
```

#### Production with Docker
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild images
docker-compose build --no-cache
```

### 🗄️ Database Management

#### Using Docker (Recommended)
```bash
# Start PostgreSQL and Redis
docker-compose up postgres redis -d

# Connect to PostgreSQL
docker exec -it eduai-postgres psql -U eduai_user -d eduai_db

# Connect to Redis
docker exec -it eduai-redis redis-cli
```

#### Local Installation
If you prefer local installations:

**PostgreSQL:**
```bash
# macOS
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib
sudo systemctl start postgresql

# Create database
createdb eduai_db
```

**Redis:**
```bash
# macOS
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server
```

### 🧪 Testing

#### Backend Testing (Jest)
```bash
cd apps/backend

# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- auth.test.ts
```

#### Frontend Testing (Vitest)
```bash
cd apps/frontend

# Run all tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests in watch mode
npm run test -- --watch
```

#### E2E Testing (Playwright)
```bash
cd apps/frontend

# Install Playwright browsers (first time only)
npx playwright install

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run specific test
npx playwright test login.spec.ts
```

### 🔧 Configuration

#### Environment Variables
The application uses environment variables for configuration. Copy `.env.example` to `.env` and update the values:

```bash
# Application
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL=postgresql://eduai_user:eduai_password@localhost:5432/eduai_db

# Redis
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173
```

### 📦 Package Management

This monorepo uses npm workspaces for package management. The root `package.json` defines workspaces for:

- `apps/*` - Applications (frontend, backend)
- `packages/*` - Shared packages

#### Adding Dependencies

**To a specific workspace:**
```bash
# Add to backend
npm install express --workspace=apps/backend

# Add to frontend
npm install axios --workspace=apps/frontend

# Add to shared package
npm install zod --workspace=packages/shared
```

**To root (affects all workspaces):**
```bash
npm install typescript --save-dev
```

#### Workspace Commands
```bash
# Run command in specific workspace
npm run build --workspace=apps/backend

# Run command in all workspaces
npm run test --workspaces

# Install dependencies for all workspaces
npm install
```

### 🔄 API Integration

The frontend is configured to work with the backend API. Key integration points:

#### API Client Setup
The frontend uses axios for API calls with a configured base client:

```typescript
// In apps/frontend/src/lib/api.ts
import axios from 'axios';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  timeout: 10000,
});

export default api;
```

#### Shared Types
Both frontend and backend use shared TypeScript types from `@eduai/shared`:

```typescript
import { User, Course, ApiResponse } from '@eduai/shared';
```

### 🚀 Deployment

#### Production Build
```bash
# Build all packages for production
npm run build

# Build specific package
npm run build:backend
npm run build:frontend
```

#### Docker Deployment
```bash
# Build and deploy with Docker Compose
docker-compose up -d

# Or build images separately
docker build -f apps/backend/Dockerfile -t eduai-backend .
docker build -f apps/frontend/Dockerfile -t eduai-frontend .
```

#### Environment-Specific Deployments
```bash
# Development environment
docker-compose -f docker-compose.dev.yml up -d

# Production environment
docker-compose -f docker-compose.yml up -d
```

### 🔒 Security Considerations

#### Environment Variables
- Never commit `.env` files to version control
- Use strong, unique JWT secrets in production
- Rotate secrets regularly
- Use environment-specific configurations

#### Database Security
- Use strong database passwords
- Enable SSL/TLS for database connections in production
- Regularly update database software
- Implement proper backup strategies

#### API Security
- Rate limiting is configured by default
- CORS is properly configured
- Input validation using Zod schemas
- JWT token expiration is enforced

### 🔧 Troubleshooting

#### Common Issues

**Port Already in Use:**
```bash
# Find process using port 3001 (backend)
lsof -i :3001
kill -9 <PID>

# Find process using port 5173 (frontend)
lsof -i :5173
kill -9 <PID>
```

**Database Connection Issues:**
```bash
# Check if PostgreSQL is running
docker ps | grep postgres

# Check PostgreSQL logs
docker logs eduai-postgres

# Reset database
docker-compose down
docker volume rm eduai-global-academy-hub_postgres_data
docker-compose up postgres -d
```

**Node Modules Issues:**
```bash
# Clean and reinstall
npm run clean
rm -rf node_modules package-lock.json
npm install
```

**TypeScript Build Issues:**
```bash
# Clean TypeScript cache
npx tsc --build --clean

# Rebuild shared package
npm run build:shared
```

#### Performance Tips

1. **Development:**
   - Use `npm run dev` for hot reloading
   - Keep Docker containers running to avoid startup time
   - Use `--watch` flags for continuous testing

2. **Production:**
   - Build with `NODE_ENV=production`
   - Use Docker multi-stage builds for smaller images
   - Enable gzip compression in nginx
   - Use Redis for caching

3. **Database:**
   - Use connection pooling
   - Add appropriate indexes
   - Monitor query performance
   - Regular database maintenance

## 5. Root Configuration

### 5.1 Create Root package.json

```json
{
  "name": "{project_name}",
  "private": true,
  "workspaces": [
    "frontend",
    "shared"
  ],
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:backend": "cd backend && poetry run uvicorn app.main:app --reload",
    "dev:frontend": "cd frontend && npm run dev",
    "test": "npm run test:backend && npm run test:frontend",
    "test:backend": "cd backend && poetry run pytest",
    "test:frontend": "cd frontend && npm run test",
    "lint": "npm run lint:backend && npm run lint:frontend",
    "lint:backend": "cd backend && poetry run ruff check . && poetry run mypy .",
    "lint:frontend": "cd frontend && npm run lint",
    "format": "npm run format:backend && npm run format:frontend",
    "format:backend": "cd backend && poetry run black .",
    "format:frontend": "cd frontend && npm run prettier",
    "e2e": "cd frontend && npm run test:e2e"
  },
  "devDependencies": {
    "concurrently": "^8.0.0"
  }
}
```

### 5.2 Create .gitignore

```
# Python
__pycache__/
*.py[cod]
*$py.class
.Python
env/
venv/
.env
.venv
.pytest_cache/
.coverage
htmlcov/
.mypy_cache/
.ruff_cache/

# Node
node_modules/
dist/
build/
.DS_Store
*.log
npm-debug.log*
.eslintcache

# IDEs
.vscode/
.idea/
*.swp
*.swo

# Project specific
*.sqlite3
*.db
.env.local
.env.*.local
coverage/
playwright-report/
test-results/
```

## 6. Infrastructure Templates

### 6.1 Docker Configuration

Create Dockerfile.backend:
```dockerfile
FROM python:3.9-slim

WORKDIR /app

RUN pip install poetry

COPY pyproject.toml poetry.lock ./
RUN poetry config virtualenvs.create false && poetry install --no-dev

COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

Create Dockerfile.frontend:
```dockerfile
FROM node:18-alpine as builder

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
```

Create docker-compose.yml:
```yaml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/app
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend

  db:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=app
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

## 7. GitHub Actions CI/CD

Create .github/workflows/ci.yml:
```yaml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  backend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - name: Install Poetry
        run: pip install poetry
      - name: Install dependencies
        run: cd backend && poetry install
      - name: Run tests
        run: cd backend && poetry run pytest
      - name: Lint
        run: cd backend && poetry run ruff check . && poetry run mypy .

  frontend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: cd frontend && npm ci
      - name: Lint
        run: cd frontend && npm run lint
      - name: Test
        run: cd frontend && npm run test
      - name: Build
        run: cd frontend && npm run build

  e2e-test:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]
    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install Playwright
        run: cd frontend && npm ci && npx playwright install --with-deps
      - name: Run E2E tests
        run: cd frontend && npm run test:e2e
```

## Usage Instructions

1. Replace `{project_name}` with your actual project name throughout all files
2. Run `poetry install` in the backend directory
3. Run `npm install` in the frontend directory
4. Create necessary .env files based on the configuration
5. Start development with `npm run dev` from the root directory

## Key Features

- **Modular Architecture**: Clean separation between frontend, backend, and shared code
- **Type Safety**: TypeScript in frontend, Pydantic in backend
- **Testing**: Unit, integration, and E2E tests with Pytest and Playwright
- **Code Quality**: ESLint, Prettier, Ruff, and MyPy for code formatting and linting
- **CI/CD**: GitHub Actions for automated testing and deployment
- **Containerization**: Docker and Docker Compose for deployment
- **Developer Experience**: Hot reloading, concurrent development servers, unified scripts

This template provides a solid foundation for a production-ready monorepo setup that follows modern best practices and conventions.
