# Environment Configuration Example
# Copy this file to .env and update the values

# Application
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL=postgresql://eduai_user:eduai_password@localhost:5432/eduai_db

# Redis
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# API Configuration
API_BASE_URL=http://localhost:3001

# Logging
LOG_LEVEL=info

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-key

# Development
DEBUG=true
ENABLE_CORS=true
