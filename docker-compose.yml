version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: eduai-postgres
    environment:
      POSTGRES_DB: eduai_db
      POSTGRES_USER: eduai_user
      POSTGRES_PASSWORD: eduai_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - eduai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U eduai_user -d eduai_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: eduai-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - eduai-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile
    container_name: eduai-backend
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: ****************************************************/eduai_db
      REDIS_URL: redis://redis:6379
      JWT_SECRET_KEY: your-super-secret-jwt-key-change-in-production
      FRONTEND_URL: http://localhost:3000
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - eduai-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Frontend Web App
  frontend:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile
    container_name: eduai-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - eduai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  eduai-network:
    driver: bridge
