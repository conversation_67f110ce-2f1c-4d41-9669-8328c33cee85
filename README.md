# 🎓 EduAI Global Academy Hub

A modern, full-stack educational platform built with Node.js, React, and TypeScript. This monorepo contains a complete learning management system with AI-powered features for global education.

## 🚀 Quick Start

```bash
# Clone and setup
git clone <your-repo-url>
cd eduai-global-academy-hub
./scripts/setup.sh

# Start development
npm run dev
```

Visit:
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React App     │    │   Express API   │    │   PostgreSQL    │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (Database)    │
│   Port: 5173    │    │   Port: 3001    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Redis       │
                    │    (Cache)      │
                    │   Port: 6379    │
                    └─────────────────┘
```

## 📦 Tech Stack

### Frontend
- **React 18** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool
- **ShadCN UI** - Component library
- **Tailwind CSS** - Styling
- **React Query** - Server state management
- **React Router** - Navigation

### Backend
- **Node.js** - Runtime
- **Express.js** - Web framework
- **TypeScript** - Type safety
- **PostgreSQL** - Primary database
- **Redis** - Caching layer
- **Winston** - Logging
- **Zod** - Schema validation

### DevOps
- **Docker** - Containerization
- **GitHub Actions** - CI/CD
- **Jest** - Backend testing
- **Vitest** - Frontend testing
- **Playwright** - E2E testing

## 📁 Project Structure

```
eduai-global-academy-hub/
├── apps/
│   ├── backend/          # Node.js/Express API
│   └── frontend/         # React/TypeScript app
├── packages/
│   └── shared/           # Shared TypeScript types & utilities
├── infrastructure/       # Database scripts & configs
├── scripts/             # Development scripts
├── .github/workflows/   # CI/CD pipelines
└── docs/               # Documentation
```

## 🛠️ Development

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- Git

### Setup
```bash
# Run setup script
./scripts/setup.sh

# Or manual setup
npm install
cp .env.example .env
# Edit .env with your configuration
```

### Available Commands
```bash
# Development
npm run dev                 # Start both frontend and backend
npm run dev:frontend        # Start only frontend
npm run dev:backend         # Start only backend

# Building
npm run build              # Build all packages
npm run build:frontend     # Build frontend only
npm run build:backend      # Build backend only

# Testing
npm run test              # Run all tests
npm run test:e2e          # Run E2E tests
npm run lint              # Lint all code
```

### Docker Development
```bash
# Start all services
docker-compose up -d

# Development mode with hot reload
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f
```

## 🌟 Features

### Current Features ✅
- **User Authentication** - Registration, login, JWT tokens
- **Course Management** - Browse, search, enroll in courses
- **Community Features** - Discussion posts, study groups
- **Responsive Design** - Mobile-first UI with dark/light themes
- **Real-time Updates** - Live data with React Query
- **Type Safety** - End-to-end TypeScript coverage

### Planned Features 🚧
- **Video Streaming** - Course video playback with progress tracking
- **Interactive Exercises** - Coding challenges and quizzes
- **AI Tutoring** - Personalized learning assistance
- **Live Sessions** - Real-time virtual classrooms
- **Certificates** - Completion certificates and badges
- **Mobile App** - React Native mobile application

## 📚 Documentation

- **[Setup Guide](docs/monorepo-template-instructions.md)** - Comprehensive setup instructions
- **[API Documentation](apps/backend/README.md)** - Backend API reference
- **[Frontend Guide](apps/frontend/README.md)** - Frontend development guide
- **[Contributing](CONTRIBUTING.md)** - How to contribute to the project

## 🚀 Deployment

### Production Deployment
```bash
# Build for production
npm run build

# Deploy with Docker
docker-compose up -d
```

### Environment Variables
Copy `.env.example` to `.env` and configure:
- Database connection strings
- JWT secrets
- API keys
- Frontend/backend URLs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- React and Node.js communities
- ShadCN UI for beautiful components
- All contributors and supporters

---

**Built with ❤️ for global education**
