{"name": "eduai-global-academy-hub", "private": true, "version": "1.0.0", "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd apps/backend && npm run dev", "dev:frontend": "cd apps/frontend && npm run dev", "build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:shared": "cd packages/shared && npm run build", "build:backend": "cd apps/backend && npm run build", "build:frontend": "cd apps/frontend && npm run build", "test": "npm run test:shared && npm run test:backend && npm run test:frontend", "test:shared": "cd packages/shared && npm run test", "test:backend": "cd apps/backend && npm run test", "test:frontend": "cd apps/frontend && npm run test", "test:e2e": "cd apps/frontend && npm run test:e2e", "lint": "npm run lint:backend && npm run lint:frontend && npm run lint:shared", "lint:backend": "cd apps/backend && npm run lint", "lint:frontend": "cd apps/frontend && npm run lint", "lint:shared": "cd packages/shared && npm run lint", "format": "npm run format:backend && npm run format:frontend && npm run format:shared", "format:backend": "cd apps/backend && npm run format", "format:frontend": "cd apps/frontend && npm run format", "format:shared": "cd packages/shared && npm run format", "clean": "npm run clean:backend && npm run clean:frontend && npm run clean:shared", "clean:backend": "cd apps/backend && npm run clean", "clean:frontend": "cd apps/frontend && npm run clean", "clean:shared": "cd packages/shared && npm run clean", "install:all": "npm install && npm run install:backend && npm run install:frontend && npm run install:shared", "install:backend": "cd apps/backend && npm install", "install:frontend": "cd apps/frontend && npm install", "install:shared": "cd packages/shared && npm install"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5", "typescript": "^5.5.3"}}