version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: eduai-postgres-dev
    environment:
      POSTGRES_DB: eduai_dev
      POSTGRES_USER: eduai_dev
      POSTGRES_PASSWORD: eduai_dev_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - eduai-dev-network

  # Redis Cache for Development
  redis-dev:
    image: redis:7-alpine
    container_name: eduai-redis-dev
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"
    networks:
      - eduai-dev-network

  # Backend API for Development (with hot reload)
  backend-dev:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile.dev
      target: development
    container_name: eduai-backend-dev
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ***********************************************************/eduai_dev
      REDIS_URL: redis://redis-dev:6379
      JWT_SECRET_KEY: dev-jwt-secret-key
      FRONTEND_URL: http://localhost:5173
    ports:
      - "3001:3001"
      - "9229:9229" # Debug port
    depends_on:
      - postgres-dev
      - redis-dev
    networks:
      - eduai-dev-network
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    command: npm run dev

  # Frontend Web App for Development (with hot reload)
  frontend-dev:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile.dev
      target: development
    container_name: eduai-frontend-dev
    environment:
      VITE_API_URL: http://localhost:3001
    ports:
      - "5173:5173"
    networks:
      - eduai-dev-network
    volumes:
      - .:/app
      - /app/node_modules
      - /app/apps/frontend/node_modules
    command: npm run dev:frontend

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  eduai-dev-network:
    driver: bridge
